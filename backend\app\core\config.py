"""
Configuration settings for Strategy Builder
"""
from pydantic_settings import BaseSettings
from typing import Optional
import os
from pathlib import Path

# Load environment variables from .env file
env_file = Path(__file__).parent.parent.parent.parent / ".env"

class Settings(BaseSettings):
    """Application settings loaded from environment variables"""
    
    # Application Settings
    DEBUG: bool = True
    HOST: str = "127.0.0.1"
    PORT: int = 8000
    
    # Database Configuration
    DB_HOST: str = "localhost"
    DB_PORT: int = 3306
    DB_USERNAME: str = "root"
    DB_PASSWORD: str = "@Oppa121089"
    DB_DATABASE: str = "strategy_builder"
    
    # Binance API Configuration
    BINANCE_API_KEY: str = ""
    BINANCE_API_SECRET: str = ""
    BINANCE_TESTNET: bool = False
    BINANCE_BASE_URL: str = "https://fapi.binance.com"
    
    # Binance Testnet (optional)
    BINANCE_TESTNET_API_KEY: Optional[str] = None
    BINANCE_TESTNET_SECRET_KEY: Optional[str] = None
    
    # MEXC API Configuration
    MEXC_API_KEY: str = ""
    MEXC_API_SECRET: str = ""
    MEXC_BASE_URL: str = "https://contract.mexc.com"
    
    @property
    def database_url(self) -> str:
        """Generate database URL for SQLAlchemy"""
        from urllib.parse import quote_plus
        encoded_password = quote_plus(self.DB_PASSWORD)
        return f"mysql+pymysql://{self.DB_USERNAME}:{encoded_password}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_DATABASE}"
    
    @property
    def binance_config(self) -> dict:
        """Get Binance configuration"""
        if self.BINANCE_TESTNET and self.BINANCE_TESTNET_API_KEY:
            return {
                "api_key": self.BINANCE_TESTNET_API_KEY,
                "api_secret": self.BINANCE_TESTNET_SECRET_KEY,
                "base_url": "https://testnet.binancefuture.com",
                "testnet": True
            }
        return {
            "api_key": self.BINANCE_API_KEY,
            "api_secret": self.BINANCE_API_SECRET,
            "base_url": self.BINANCE_BASE_URL,
            "testnet": False
        }
    
    @property
    def mexc_config(self) -> dict:
        """Get MEXC configuration"""
        return {
            "api_key": self.MEXC_API_KEY,
            "api_secret": self.MEXC_API_SECRET,
            "base_url": self.MEXC_BASE_URL
        }
    
    class Config:
        env_file = str(env_file) if env_file.exists() else None
        case_sensitive = True

# Global settings instance
settings = Settings()
