"""
Enhanced OHLCV Data API Endpoints with proper validation and error handling
"""
from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import logging
import asyncio

from app.core.database import get_db, DatabaseError
from app.core.exceptions import ExchangeError, ValidationError, RateLimitError
from app.core.validators import OHLCVFetchRequest, SymbolValidator, TimeframeValidator, ExchangeValidator
from app.models.ohlcv import OHLCVData
from app.services.binance_client import BinanceClient
from app.services.mexc_client import MEXCClient

logger = logging.getLogger(__name__)
router = APIRouter()

# Response models for better API documentation
class OHLCVResponse(BaseModel):
    """OHLCV response model"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class HealthResponse(BaseModel):
    """Health check response model"""
    success: bool
    exchange: str
    message: str
    rate_limit_status: Optional[Dict[str, Any]] = None

@router.post("/fetch", response_model=OHLCVResponse)
async def fetch_ohlcv_data(
    request: OHLCVFetchRequest,
    db: Session = Depends(get_db)
) -> OHLCVResponse:
    """
    Fetch OHLCV data from exchange and store in database

    This endpoint fetches candlestick data from the specified exchange
    and stores it in the database, avoiding duplicates.
    """
    try:
        # Initialize exchange client
        if request.exchange == "binance":
            client = BinanceClient()
        elif request.exchange == "mexc":
            client = MEXCClient()
        else:
            raise ValidationError(f"Unsupported exchange: {request.exchange}")

        # Fetch data from exchange
        ohlcv_data = await client.get_klines(
            symbol=request.symbol,
            interval=request.timeframe,
            limit=request.limit,
            start_time=request.start_time,
            end_time=request.end_time
        )

        if not ohlcv_data:
            return OHLCVResponse(
                success=False,
                message="No data found",
                error="No data available for the specified parameters"
            )

        # Store in database with batch processing
        stored_count = await _store_ohlcv_data(db, request.symbol, request.timeframe, ohlcv_data)

        logger.info(f"Fetched {len(ohlcv_data)} candles, stored {stored_count} new records for {request.symbol}")

        return OHLCVResponse(
            success=True,
            message=f"Successfully fetched {len(ohlcv_data)} candles, stored {stored_count} new records",
            data={
                "symbol": request.symbol,
                "timeframe": request.timeframe,
                "exchange": request.exchange,
                "total_fetched": len(ohlcv_data),
                "new_records": stored_count,
                "data_range": {
                    "start": ohlcv_data[0]['timestamp'].isoformat() if ohlcv_data else None,
                    "end": ohlcv_data[-1]['timestamp'].isoformat() if ohlcv_data else None
                }
            }
        )

    except ValidationError as e:
        logger.warning(f"Validation error: {e}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

    except RateLimitError as e:
        logger.warning(f"Rate limit error: {e}")
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=f"Rate limit exceeded. Retry after {e.retry_after} seconds" if e.retry_after else "Rate limit exceeded"
        )

    except ExchangeError as e:
        logger.error(f"Exchange error: {e}")
        raise HTTPException(status_code=status.HTTP_502_BAD_GATEWAY, detail=f"Exchange error: {e.message}")

    except DatabaseError as e:
        logger.error(f"Database error: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Database operation failed")

    except Exception as e:
        logger.error(f"Unexpected error fetching OHLCV data: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")

async def _store_ohlcv_data(db: Session, symbol: str, timeframe: str, ohlcv_data: List[Dict]) -> int:
    """Store OHLCV data in database with batch processing and duplicate checking"""
    stored_count = 0
    batch_size = 100

    try:
        for i in range(0, len(ohlcv_data), batch_size):
            batch = ohlcv_data[i:i + batch_size]

            for data in batch:
                # Check if record already exists
                existing = db.query(OHLCVData).filter(
                    OHLCVData.symbol == symbol,
                    OHLCVData.timeframe == timeframe,
                    OHLCVData.timestamp == data['timestamp']
                ).first()

                if not existing:
                    ohlcv_record = OHLCVData(
                        symbol=symbol,
                        timeframe=timeframe,
                        timestamp=data['timestamp'],
                        open=data['open'],
                        high=data['high'],
                        low=data['low'],
                        close=data['close'],
                        volume=data['volume']
                    )
                    db.add(ohlcv_record)
                    stored_count += 1

            # Commit batch
            db.commit()

        return stored_count

    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"Database error storing OHLCV data: {e}")
        raise DatabaseError(f"Failed to store OHLCV data: {e}")
    except Exception as e:
        db.rollback()
        logger.error(f"Unexpected error storing OHLCV data: {e}")
        raise

@router.get("/data")
async def get_ohlcv_data(
    symbol: str = Query(..., description="Trading pair symbol"),
    timeframe: str = Query(..., description="Timeframe"),
    limit: int = Query(500, description="Number of candles to return", ge=1, le=2000),
    start_time: Optional[str] = Query(None, description="Start time (ISO format)"),
    end_time: Optional[str] = Query(None, description="End time (ISO format)"),
    db: Session = Depends(get_db)
):
    """Get OHLCV data from database"""
    try:
        query = db.query(OHLCVData).filter(
            OHLCVData.symbol == symbol.upper(),
            OHLCVData.timeframe == timeframe
        )
        
        # Apply time filters if provided
        if start_time:
            start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            query = query.filter(OHLCVData.timestamp >= start_dt)
        
        if end_time:
            end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
            query = query.filter(OHLCVData.timestamp <= end_dt)
        
        # Order by timestamp and apply limit
        ohlcv_records = query.order_by(OHLCVData.timestamp.desc()).limit(limit).all()
        
        if not ohlcv_records:
            raise HTTPException(status_code=404, detail="No data found")
        
        # Reverse to get chronological order
        ohlcv_records.reverse()
        
        # Convert to response format
        data = [record.to_dict() for record in ohlcv_records]
        
        return {
            "success": True,
            "data": {
                "symbol": symbol.upper(),
                "timeframe": timeframe,
                "count": len(data),
                "ohlcv": data
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting OHLCV data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/symbols")
async def get_available_symbols(
    exchange: str = Query("binance", description="Exchange (binance or mexc)"),
    db: Session = Depends(get_db)
):
    """Get available trading symbols"""
    try:
        # Get unique symbols from database
        symbols = db.query(OHLCVData.symbol).distinct().all()
        db_symbols = [symbol[0] for symbol in symbols]
        
        # Also get from exchange
        exchange_symbols = []
        try:
            if exchange.lower() == "binance":
                client = BinanceClient()
                exchange_info = client.get_exchange_info()
                exchange_symbols = [s['symbol'] for s in exchange_info.get('symbols', [])]
            elif exchange.lower() == "mexc":
                client = MEXCClient()
                contracts = client.get_contracts()
                if contracts.get('success') and contracts.get('data'):
                    exchange_symbols = [c['symbol'] for c in contracts['data']]
        except Exception as e:
            logger.warning(f"Could not fetch symbols from {exchange}: {e}")
        
        return {
            "success": True,
            "data": {
                "exchange": exchange,
                "database_symbols": db_symbols,
                "exchange_symbols": exchange_symbols[:50]  # Limit for response size
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting symbols: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/test-connection")
async def test_exchange_connection(
    exchange: str = Query("binance", description="Exchange to test")
):
    """Test connection to exchange API"""
    try:
        if exchange.lower() == "binance":
            client = BinanceClient()
            success = client.test_connection()
        elif exchange.lower() == "mexc":
            client = MEXCClient()
            success = client.test_connection()
        else:
            raise HTTPException(status_code=400, detail="Unsupported exchange")
        
        return {
            "success": success,
            "exchange": exchange,
            "message": f"{exchange} connection {'successful' if success else 'failed'}"
        }
        
    except Exception as e:
        logger.error(f"Error testing {exchange} connection: {e}")
        raise HTTPException(status_code=500, detail=str(e))
