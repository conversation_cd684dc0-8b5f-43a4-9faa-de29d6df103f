"""
Database configuration and connection management
"""
from sqlalchemy import create_engine, MetaData, event
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from sqlalchemy.exc import SQLAlchemyError, DisconnectionError
from contextlib import contextmanager
import logging
import pymysql
from typing import Generator, Optional
import time

from app.core.config import settings

# Install PyMySQL as MySQLdb
pymysql.install_as_MySQLdb()

# Configure logging
logger = logging.getLogger(__name__)

class DatabaseError(Exception):
    """Custom database error"""
    pass

class ConnectionError(DatabaseError):
    """Database connection error"""
    pass

# Database engine with connection pooling using PyMySQL
engine = create_engine(
    settings.database_url,
    poolclass=QueuePool,
    pool_size=settings.DB_POOL_SIZE,
    max_overflow=settings.DB_MAX_OVERFLOW,
    pool_pre_ping=True,
    pool_recycle=3600,  # Recycle connections every hour
    echo=settings.DEBUG,  # Log SQL queries in debug mode
    connect_args={
        "charset": "utf8mb4",
        "use_unicode": True,
        "autocommit": False,
        "connect_timeout": 30,
        "read_timeout": 30,
        "write_timeout": 30
    }
)

# Add connection event listeners for better error handling
@event.listens_for(engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """Set connection parameters"""
    if hasattr(dbapi_connection, 'ping'):
        try:
            dbapi_connection.ping(reconnect=True)
        except Exception as e:
            logger.warning(f"Connection ping failed: {e}")

@event.listens_for(engine, "checkout")
def checkout_listener(dbapi_connection, connection_record, connection_proxy):
    """Test connection on checkout"""
    try:
        dbapi_connection.ping(reconnect=False)
    except Exception:
        # Connection is invalid, raise DisconnectionError
        raise DisconnectionError("Connection lost")

# Session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for ORM models
Base = declarative_base()

# Metadata for table creation
metadata = MetaData()

async def init_db() -> None:
    """Initialize database and create tables with proper error handling"""
    try:
        # Import all models to ensure they are registered
        from app.models import ohlcv, indicators, trades

        # Test connection first
        if not await test_connection():
            raise ConnectionError("Cannot establish database connection")

        # Create all tables
        Base.metadata.create_all(bind=engine)
        logger.info("Database initialized successfully")

    except SQLAlchemyError as e:
        logger.error(f"SQLAlchemy error during database initialization: {e}")
        raise DatabaseError(f"Database initialization failed: {e}")
    except Exception as e:
        logger.error(f"Unexpected error during database initialization: {e}")
        raise DatabaseError(f"Database initialization failed: {e}")

def get_db() -> Generator[Session, None, None]:
    """Dependency to get database session with proper error handling"""
    db = SessionLocal()
    try:
        yield db
    except SQLAlchemyError as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise DatabaseError(f"Database operation failed: {e}")
    except Exception as e:
        logger.error(f"Unexpected database error: {e}")
        db.rollback()
        raise
    finally:
        db.close()

@contextmanager
def get_db_context() -> Generator[Session, None, None]:
    """Context manager for database sessions"""
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception as e:
        db.rollback()
        logger.error(f"Database transaction failed: {e}")
        raise
    finally:
        db.close()

async def test_connection() -> bool:
    """Test database connection using PyMySQL with retry logic"""
    max_retries = 3
    retry_delay = 1

    for attempt in range(max_retries):
        try:
            # Use direct connection test instead of SQLAlchemy text
            import pymysql
            from app.core.config import settings

            connection = pymysql.connect(
                host=settings.DB_HOST,
                port=settings.DB_PORT,
                user=settings.DB_USERNAME,
                password=settings.DB_PASSWORD.get_secret_value() if hasattr(settings.DB_PASSWORD, 'get_secret_value') else str(settings.DB_PASSWORD),
                database=settings.DB_DATABASE,
                charset='utf8mb4'
            )

            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            cursor.close()
            connection.close()

            if result and result[0] == 1:
                logger.info("PyMySQL database connection successful")
                return True
            else:
                logger.error("Database connection test failed - unexpected result")
                return False

        except Exception as e:
            logger.warning(f"Database connection attempt {attempt + 1} failed: {e}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff
            else:
                logger.error(f"PyMySQL database connection failed after {max_retries} attempts: {e}")
                return False

    return False

async def health_check() -> dict:
    """Comprehensive database health check"""
    start_time = time.time()

    try:
        # Test basic connection
        connection_ok = await test_connection()

        # Test pool status
        pool = engine.pool
        pool_status = {
            "size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid()
        }

        response_time = time.time() - start_time

        return {
            "status": "healthy" if connection_ok else "unhealthy",
            "connection": connection_ok,
            "response_time_ms": round(response_time * 1000, 2),
            "pool_status": pool_status,
            "database": settings.DB_DATABASE,
            "host": settings.DB_HOST
        }

    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "response_time_ms": round((time.time() - start_time) * 1000, 2)
        }
